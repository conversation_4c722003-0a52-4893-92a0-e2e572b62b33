# دليل التشغيل السريع - نظام شؤون الموظفين

## التشغيل السريع (5 دقائق)

### على Windows:
1. انقر نقراً مزدوجاً على `start.bat`
2. انتظر حتى يكتمل التثبيت
3. افتح المتصفح على `http://localhost:5000`

### على Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

### أو باستخدام Python مباشرة:
```bash
python run.py
```

## بيانات تسجيل الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| مدير النظام | `admin` | `admin123` |
| موظف HR | `hr` | `hr123` |

## الوظائف الأساسية

### 1. إضافة موظف جديد
- اذهب إلى **الموظفين** > **إضافة موظف**
- املأ البيانات المطلوبة
- ارفع الصورة الشخصية (اختياري)
- احفظ البيانات

### 2. تسجيل الحضور
- اذهب إلى **الحضور** > **تسجيل سريع**
- أدخل الرقم الوظيفي
- اختر نوع العملية (دخول/خروج)
- اضغط تسجيل

### 3. عرض التقارير
- اذهب إلى **التقارير**
- اختر نوع التقرير المطلوب
- حدد الفترة الزمنية
- اعرض أو اطبع التقرير

## اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `F1` | تسجيل دخول سريع |
| `F2` | تسجيل خروج سريع |
| `Ctrl+/` | البحث السريع |
| `Escape` | إلغاء/إغلاق |

## حل المشاكل الشائعة

### المشكلة: خطأ في قاعدة البيانات
**الحل:**
```bash
python init_db.py
```

### المشكلة: خطأ في المتطلبات
**الحل:**
```bash
pip install -r requirements.txt
```

### المشكلة: لا يمكن الوصول للنظام
**الحل:**
- تأكد من أن المنفذ 5000 غير مستخدم
- جرب عنوان مختلف: `http://127.0.0.1:5000`

## الدعم الفني

للحصول على المساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من ملفات السجل في مجلد `logs/`
3. أنشئ issue في المستودع

---
**نصيحة:** احفظ نسخة احتياطية من قاعدة البيانات بانتظام!
