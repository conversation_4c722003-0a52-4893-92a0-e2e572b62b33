from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, <PERSON>wordField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={"class": "form-control", "placeholder": "أدخل اسم المستخدم"})
    password = PasswordField('كلمة المرور', validators=[DataRequired()], 
                           render_kw={"class": "form-control", "placeholder": "أدخل كلمة المرور"})
    remember_me = BooleanField('تذكرني', render_kw={"class": "form-check-input"})
    submit = SubmitField('تسجيل الدخول', render_kw={"class": "btn btn-primary w-100"})

class RegistrationForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)], 
                          render_kw={"class": "form-control", "placeholder": "أدخل اسم المستخدم"})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()], 
                       render_kw={"class": "form-control", "placeholder": "أدخل البريد الإلكتروني"})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)], 
                           render_kw={"class": "form-control", "placeholder": "أدخل كلمة المرور"})
    password2 = PasswordField('تأكيد كلمة المرور', 
                            validators=[DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')], 
                            render_kw={"class": "form-control", "placeholder": "أعد إدخال كلمة المرور"})
    role = SelectField('الدور', choices=[('user', 'مستخدم عادي'), ('hr', 'موظف HR'), ('admin', 'مدير')], 
                      default='user', render_kw={"class": "form-select"})
    submit = SubmitField('إنشاء حساب', render_kw={"class": "btn btn-success w-100"})

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد آخر.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()], 
                                   render_kw={"class": "form-control", "placeholder": "أدخل كلمة المرور الحالية"})
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)], 
                               render_kw={"class": "form-control", "placeholder": "أدخل كلمة المرور الجديدة"})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة', 
                                validators=[DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')], 
                                render_kw={"class": "form-control", "placeholder": "أعد إدخال كلمة المرور الجديدة"})
    submit = SubmitField('تغيير كلمة المرور', render_kw={"class": "btn btn-warning w-100"})

class UserManagementForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)], 
                          render_kw={"class": "form-control"})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()], 
                       render_kw={"class": "form-control"})
    role = SelectField('الدور', choices=[('user', 'مستخدم عادي'), ('hr', 'موظف HR'), ('admin', 'مدير')], 
                      render_kw={"class": "form-select"})
    is_active = BooleanField('نشط', render_kw={"class": "form-check-input"})
    submit = SubmitField('حفظ التغييرات', render_kw={"class": "btn btn-primary"})
