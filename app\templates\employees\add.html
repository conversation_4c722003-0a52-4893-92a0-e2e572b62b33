{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-person-plus"></i> إضافة موظف جديد</h1>
            <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-right"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="employeeForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- Personal Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="bi bi-person"></i> البيانات الشخصية
                            </h5>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.first_name() }}
                                {{ form.first_name.label() }}
                            </div>
                            {% if form.first_name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.last_name() }}
                                {{ form.last_name.label() }}
                            </div>
                            {% if form.last_name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.national_id() }}
                                {{ form.national_id.label() }}
                            </div>
                            {% if form.national_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.national_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.phone() }}
                                {{ form.phone.label() }}
                            </div>
                            {% if form.phone.errors %}
                                <div class="text-danger small">
                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">{{ form.birth_date.label.text }}</label>
                            {{ form.birth_date() }}
                            {% if form.birth_date.errors %}
                                <div class="text-danger small">
                                    {% for error in form.birth_date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.birth_place() }}
                                {{ form.birth_place.label() }}
                            </div>
                            {% if form.birth_place.errors %}
                                <div class="text-danger small">
                                    {% for error in form.birth_place.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="form-floating">
                                {{ form.address() }}
                                {{ form.address.label() }}
                            </div>
                            {% if form.address.errors %}
                                <div class="text-danger small">
                                    {% for error in form.address.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Job Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="bi bi-briefcase"></i> البيانات الوظيفية
                            </h5>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.employee_number() }}
                                {{ form.employee_number.label() }}
                            </div>
                            {% if form.employee_number.errors %}
                                <div class="text-danger small">
                                    {% for error in form.employee_number.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.job_title() }}
                                {{ form.job_title.label() }}
                            </div>
                            {% if form.job_title.errors %}
                                <div class="text-danger small">
                                    {% for error in form.job_title.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.qualification() }}
                                {{ form.qualification.label() }}
                            </div>
                            {% if form.qualification.errors %}
                                <div class="text-danger small">
                                    {% for error in form.qualification.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">{{ form.contract_type.label.text }}</label>
                            {{ form.contract_type() }}
                            {% if form.contract_type.errors %}
                                <div class="text-danger small">
                                    {% for error in form.contract_type.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">{{ form.hire_date.label.text }}</label>
                            {{ form.hire_date() }}
                            {% if form.hire_date.errors %}
                                <div class="text-danger small">
                                    {% for error in form.hire_date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ form.start_date.label.text }}</label>
                            {{ form.start_date() }}
                            {% if form.start_date.errors %}
                                <div class="text-danger small">
                                    {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                {{ form.job_grade() }}
                                {{ form.job_grade.label() }}
                            </div>
                            {% if form.job_grade.errors %}
                                <div class="text-danger small">
                                    {% for error in form.job_grade.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.allowance() }}
                                {{ form.allowance.label() }}
                            </div>
                            {% if form.allowance.errors %}
                                <div class="text-danger small">
                                    {% for error in form.allowance.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 d-flex align-items-center">
                            <div class="form-check">
                                {{ form.is_active() }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Files Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="bi bi-file-earmark"></i> المرفقات
                            </h5>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">{{ form.photo.label.text }}</label>
                            {{ form.photo() }}
                            {% if form.photo.errors %}
                                <div class="text-danger small">
                                    {% for error in form.photo.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الحد الأقصى: 5MB، الأنواع المسموحة: JPG, PNG</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">{{ form.identity_proof.label.text }}</label>
                            {{ form.identity_proof() }}
                            {% if form.identity_proof.errors %}
                                <div class="text-danger small">
                                    {% for error in form.identity_proof.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الحد الأقصى: 10MB، الأنواع المسموحة: JPG, PNG, PDF</div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                                <a href="{{ url_for('employees.index') }}" class="btn btn-secondary btn-lg">إلغاء</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('employeeForm');
    form.addEventListener('submit', function(e) {
        if (!hrSystem.validateForm('employeeForm')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
