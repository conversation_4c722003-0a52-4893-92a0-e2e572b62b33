from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.attendance import bp
from app.attendance.forms import AttendanceForm, LeaveForm, AttendanceSearchForm, QuickAttendanceForm, LeaveApprovalForm
from app.models import Employee, Attendance, Leave
from app.auth.routes import hr_required, log_action
from datetime import datetime, date, time
from sqlalchemy import and_, func

@bp.route('/')
@login_required
def index():
    form = AttendanceSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Attendance.query.join(Employee)
    
    # Apply filters
    employee_id = request.args.get('employee_id', 0, type=int)
    if employee_id:
        query = query.filter(Attendance.employee_id == employee_id)
    
    start_date = request.args.get('start_date')
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Attendance.date >= start_date)
        except ValueError:
            pass
    
    end_date = request.args.get('end_date')
    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Attendance.date <= end_date)
        except ValueError:
            pass
    
    status = request.args.get('status', '')
    if status:
        query = query.filter(Attendance.status == status)
    
    # Order by date (newest first)
    query = query.order_by(Attendance.date.desc(), Attendance.created_at.desc())
    
    attendances = query.paginate(page=page, per_page=20, error_out=False)
    
    return render_template('attendance/index.html', 
                         title='سجل الحضور والانصراف', 
                         attendances=attendances, 
                         form=form)

@bp.route('/add', methods=['GET', 'POST'])
@hr_required
def add():
    form = AttendanceForm()
    if form.validate_on_submit():
        # Check if attendance already exists for this employee and date
        existing = Attendance.query.filter_by(
            employee_id=form.employee_id.data,
            date=form.date.data
        ).first()
        
        if existing:
            flash('سجل الحضور لهذا الموظف في هذا التاريخ موجود بالفعل.', 'error')
            return redirect(url_for('attendance.edit', id=existing.id))
        
        attendance = Attendance(
            employee_id=form.employee_id.data,
            date=form.date.data,
            check_in=form.check_in.data,
            check_out=form.check_out.data,
            break_start=form.break_start.data,
            break_end=form.break_end.data,
            status=form.status.data,
            notes=form.notes.data
        )
        
        # Calculate hours
        attendance.calculate_hours()
        
        db.session.add(attendance)
        db.session.commit()
        
        log_action('ATTENDANCE_CREATED', 'attendance', attendance.id, 
                  new_values={'employee': attendance.employee.full_name, 'date': str(attendance.date)})
        
        flash('تم إضافة سجل الحضور بنجاح!', 'success')
        return redirect(url_for('attendance.index'))
    
    return render_template('attendance/add.html', title='إضافة سجل حضور', form=form)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@hr_required
def edit(id):
    attendance = Attendance.query.get_or_404(id)
    form = AttendanceForm(obj=attendance)
    
    if form.validate_on_submit():
        old_values = {
            'check_in': str(attendance.check_in) if attendance.check_in else None,
            'check_out': str(attendance.check_out) if attendance.check_out else None,
            'status': attendance.status
        }
        
        form.populate_obj(attendance)
        attendance.calculate_hours()
        db.session.commit()
        
        new_values = {
            'check_in': str(attendance.check_in) if attendance.check_in else None,
            'check_out': str(attendance.check_out) if attendance.check_out else None,
            'status': attendance.status
        }
        
        log_action('ATTENDANCE_UPDATED', 'attendance', attendance.id, old_values, new_values)
        
        flash('تم تحديث سجل الحضور بنجاح!', 'success')
        return redirect(url_for('attendance.index'))
    
    return render_template('attendance/edit.html',
                         title='تعديل سجل الحضور',
                         form=form, attendance=attendance)

@bp.route('/quick', methods=['GET', 'POST'])
@login_required
def quick_attendance():
    form = QuickAttendanceForm()
    if form.validate_on_submit():
        employee = Employee.query.filter_by(
            employee_number=form.employee_number.data,
            is_active=True
        ).first()

        if not employee:
            flash('الرقم الوظيفي غير موجود أو الموظف غير نشط.', 'error')
            return redirect(url_for('attendance.quick_attendance'))

        today = date.today()
        attendance = Attendance.query.filter_by(
            employee_id=employee.id,
            date=today
        ).first()

        if not attendance:
            attendance = Attendance(
                employee_id=employee.id,
                date=today,
                status='present'
            )
            db.session.add(attendance)

        current_time = datetime.now().time()

        if form.action.data == 'check_in':
            if attendance.check_in:
                flash(f'الموظف {employee.full_name} سجل دخوله بالفعل اليوم.', 'warning')
            else:
                attendance.check_in = current_time
                # Check if late (assuming work starts at 8:00 AM)
                if current_time > time(8, 0):
                    attendance.status = 'late'
                flash(f'تم تسجيل دخول الموظف {employee.full_name} بنجاح.', 'success')

        elif form.action.data == 'check_out':
            if not attendance.check_in:
                flash(f'الموظف {employee.full_name} لم يسجل دخوله بعد.', 'error')
            elif attendance.check_out:
                flash(f'الموظف {employee.full_name} سجل خروجه بالفعل اليوم.', 'warning')
            else:
                attendance.check_out = current_time
                attendance.calculate_hours()
                flash(f'تم تسجيل خروج الموظف {employee.full_name} بنجاح.', 'success')

        db.session.commit()
        log_action(f'QUICK_{form.action.data.upper()}', 'attendance', attendance.id,
                  new_values={'employee': employee.full_name, 'time': str(current_time)})

        return redirect(url_for('attendance.quick_attendance'))

    return render_template('attendance/quick.html', title='تسجيل سريع للحضور', form=form)

@bp.route('/leaves')
@login_required
def leaves():
    page = request.args.get('page', 1, type=int)

    # Filter based on user role
    if current_user.role == 'admin' or current_user.role == 'hr':
        query = Leave.query.join(Employee)
    else:
        # Regular users can only see their own leaves if they have an employee record
        employee = Employee.query.filter_by(created_by=current_user.id).first()
        if employee:
            query = Leave.query.filter_by(employee_id=employee.id)
        else:
            query = Leave.query.filter_by(id=0)  # No results

    status = request.args.get('status', '')
    if status:
        query = query.filter(Leave.status == status)

    leaves = query.order_by(Leave.applied_date.desc()).paginate(
        page=page, per_page=20, error_out=False)

    return render_template('attendance/leaves.html', title='طلبات الإجازات', leaves=leaves)

@bp.route('/leaves/add', methods=['GET', 'POST'])
@hr_required
def add_leave():
    form = LeaveForm()
    if form.validate_on_submit():
        # Calculate days count
        days_count = (form.end_date.data - form.start_date.data).days + 1

        leave = Leave(
            employee_id=form.employee_id.data,
            leave_type=form.leave_type.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            days_count=days_count,
            reason=form.reason.data,
            notes=form.notes.data,
            status='pending'
        )

        db.session.add(leave)
        db.session.commit()

        log_action('LEAVE_CREATED', 'leave', leave.id,
                  new_values={'employee': leave.employee.full_name, 'type': leave.leave_type, 'days': days_count})

        flash('تم تقديم طلب الإجازة بنجاح!', 'success')
        return redirect(url_for('attendance.leaves'))

    return render_template('attendance/add_leave.html', title='تقديم طلب إجازة', form=form)
