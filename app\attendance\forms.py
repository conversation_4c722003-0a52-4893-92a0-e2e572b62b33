from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, DateField, TimeField, SelectField, TextAreaField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Optional, NumberRange, ValidationError
from app.models import Employee, Attendance, Leave
from datetime import date, datetime, time

class AttendanceForm(FlaskForm):
    employee_id = SelectField('الموظف', coerce=int, validators=[DataRequired()], 
                             render_kw={"class": "form-select"})
    date = DateField('التاريخ', validators=[DataRequired()], default=date.today,
                    render_kw={"class": "form-control"})
    check_in = TimeField('وقت الدخول', validators=[Optional()], 
                        render_kw={"class": "form-control"})
    check_out = TimeField('وقت الخروج', validators=[Optional()], 
                         render_kw={"class": "form-control"})
    break_start = TimeField('بداية الاستراحة', validators=[Optional()], 
                           render_kw={"class": "form-control"})
    break_end = TimeField('نهاية الاستراحة', validators=[Optional()], 
                         render_kw={"class": "form-control"})
    status = SelectField('الحالة', 
                        choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('half_day', 'نصف يوم')], 
                        validators=[DataRequired()], 
                        render_kw={"class": "form-select"})
    notes = TextAreaField('ملاحظات', validators=[Optional()], 
                         render_kw={"class": "form-control", "rows": "3"})
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-primary"})

    def __init__(self, *args, **kwargs):
        super(AttendanceForm, self).__init__(*args, **kwargs)
        self.employee_id.choices = [(0, 'اختر الموظف')] + [
            (emp.id, f"{emp.full_name} - {emp.employee_number}") 
            for emp in Employee.query.filter_by(is_active=True).order_by(Employee.first_name).all()
        ]

    def validate_check_out(self, check_out):
        if self.check_in.data and check_out.data:
            if check_out.data <= self.check_in.data:
                raise ValidationError('وقت الخروج يجب أن يكون بعد وقت الدخول.')

    def validate_break_end(self, break_end):
        if self.break_start.data and break_end.data:
            if break_end.data <= self.break_start.data:
                raise ValidationError('نهاية الاستراحة يجب أن تكون بعد بدايتها.')

class LeaveForm(FlaskForm):
    employee_id = SelectField('الموظف', coerce=int, validators=[DataRequired()], 
                             render_kw={"class": "form-select"})
    leave_type = SelectField('نوع الإجازة', 
                            choices=[('annual', 'إجازة سنوية'), ('sick', 'إجازة مرضية'), 
                                   ('emergency', 'إجازة طارئة'), ('maternity', 'إجازة أمومة')], 
                            validators=[DataRequired()], 
                            render_kw={"class": "form-select"})
    start_date = DateField('تاريخ البداية', validators=[DataRequired()], 
                          render_kw={"class": "form-control"})
    end_date = DateField('تاريخ النهاية', validators=[DataRequired()], 
                        render_kw={"class": "form-control"})
    reason = TextAreaField('السبب', validators=[Optional()], 
                          render_kw={"class": "form-control", "rows": "3"})
    notes = TextAreaField('ملاحظات', validators=[Optional()], 
                         render_kw={"class": "form-control", "rows": "2"})
    submit = SubmitField('تقديم الطلب', render_kw={"class": "btn btn-primary"})

    def __init__(self, *args, **kwargs):
        super(LeaveForm, self).__init__(*args, **kwargs)
        self.employee_id.choices = [(0, 'اختر الموظف')] + [
            (emp.id, f"{emp.full_name} - {emp.employee_number}") 
            for emp in Employee.query.filter_by(is_active=True).order_by(Employee.first_name).all()
        ]

    def validate_end_date(self, end_date):
        if self.start_date.data and end_date.data:
            if end_date.data < self.start_date.data:
                raise ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية.')

    def validate_start_date(self, start_date):
        if start_date.data and start_date.data < date.today():
            raise ValidationError('لا يمكن تقديم طلب إجازة لتاريخ سابق.')

class AttendanceSearchForm(FlaskForm):
    employee_id = SelectField('الموظف', coerce=int, validators=[Optional()], 
                             render_kw={"class": "form-select"})
    start_date = DateField('من تاريخ', validators=[Optional()], 
                          render_kw={"class": "form-control"})
    end_date = DateField('إلى تاريخ', validators=[Optional()], 
                        render_kw={"class": "form-control"})
    status = SelectField('الحالة', 
                        choices=[('', 'جميع الحالات'), ('present', 'حاضر'), ('absent', 'غائب'), 
                               ('late', 'متأخر'), ('half_day', 'نصف يوم')], 
                        validators=[Optional()], 
                        render_kw={"class": "form-select"})
    submit = SubmitField('بحث', render_kw={"class": "btn btn-outline-primary"})

    def __init__(self, *args, **kwargs):
        super(AttendanceSearchForm, self).__init__(*args, **kwargs)
        self.employee_id.choices = [(0, 'جميع الموظفين')] + [
            (emp.id, f"{emp.full_name} - {emp.employee_number}") 
            for emp in Employee.query.filter_by(is_active=True).order_by(Employee.first_name).all()
        ]

class QuickAttendanceForm(FlaskForm):
    employee_number = StringField('الرقم الوظيفي', validators=[DataRequired()], 
                                 render_kw={"class": "form-control", "placeholder": "أدخل الرقم الوظيفي"})
    action = SelectField('العملية', 
                        choices=[('check_in', 'تسجيل دخول'), ('check_out', 'تسجيل خروج')], 
                        validators=[DataRequired()], 
                        render_kw={"class": "form-select"})
    submit = SubmitField('تسجيل', render_kw={"class": "btn btn-success"})

class LeaveApprovalForm(FlaskForm):
    status = SelectField('القرار', 
                        choices=[('approved', 'موافقة'), ('rejected', 'رفض')], 
                        validators=[DataRequired()], 
                        render_kw={"class": "form-select"})
    notes = TextAreaField('ملاحظات', validators=[Optional()], 
                         render_kw={"class": "form-control", "rows": "3"})
    submit = SubmitField('حفظ القرار', render_kw={"class": "btn btn-primary"})
