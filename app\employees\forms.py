from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, DateField, SelectField, FloatField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from app.models import Employee
from datetime import date

class EmployeeForm(FlaskForm):
    # Personal Information
    first_name = StringField('الاسم الأول', validators=[DataRequired(), Length(max=100)], 
                            render_kw={"class": "form-control", "placeholder": "أدخل الاسم الأول"})
    last_name = StringField('اللقب', validators=[DataRequired(), Length(max=100)], 
                           render_kw={"class": "form-control", "placeholder": "أدخل اللقب"})
    national_id = StringField('الرقم الوطني', validators=[DataRequired(), Length(max=20)], 
                             render_kw={"class": "form-control", "placeholder": "أدخل الرقم الوطني"})
    birth_date = DateField('تاريخ الميلاد', validators=[Optional()], 
                          render_kw={"class": "form-control"})
    birth_place = StringField('مكان الميلاد', validators=[Optional(), Length(max=200)], 
                             render_kw={"class": "form-control", "placeholder": "أدخل مكان الميلاد"})
    address = TextAreaField('العنوان', validators=[Optional()], 
                           render_kw={"class": "form-control", "rows": "3", "placeholder": "أدخل العنوان"})
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)], 
                       render_kw={"class": "form-control", "placeholder": "أدخل رقم الهاتف"})
    
    # Job Information
    employee_number = StringField('الرقم الوظيفي', validators=[DataRequired(), Length(max=20)], 
                                 render_kw={"class": "form-control", "placeholder": "أدخل الرقم الوظيفي"})
    job_title = StringField('المسمى الوظيفي', validators=[DataRequired(), Length(max=200)], 
                           render_kw={"class": "form-control", "placeholder": "أدخل المسمى الوظيفي"})
    qualification = StringField('المؤهل', validators=[Optional(), Length(max=200)], 
                               render_kw={"class": "form-control", "placeholder": "أدخل المؤهل"})
    hire_date = DateField('تاريخ التعيين', validators=[DataRequired()], 
                         render_kw={"class": "form-control"})
    start_date = DateField('تاريخ المباشرة', validators=[Optional()], 
                          render_kw={"class": "form-control"})
    contract_type = SelectField('نوع التعاقد', 
                               choices=[('تعيين', 'تعيين'), ('عقد', 'عقد'), ('ندب', 'ندب'), ('انتقال', 'انتقال')], 
                               validators=[Optional()], 
                               render_kw={"class": "form-select"})
    job_grade = StringField('الدرجة الوظيفية', validators=[Optional(), Length(max=20)], 
                           render_kw={"class": "form-control", "placeholder": "أدخل الدرجة الوظيفية"})
    allowance = FloatField('العلاوة', validators=[Optional(), NumberRange(min=0)], 
                          render_kw={"class": "form-control", "placeholder": "أدخل العلاوة"})
    
    # Files
    photo = FileField('الصورة الشخصية', 
                     validators=[FileAllowed(['jpg', 'png', 'jpeg'], 'الصور فقط!')], 
                     render_kw={"class": "form-control"})
    identity_proof = FileField('إثبات الهوية', 
                              validators=[FileAllowed(['jpg', 'png', 'jpeg', 'pdf'], 'الصور و PDF فقط!')], 
                              render_kw={"class": "form-control"})
    
    # Status
    is_active = BooleanField('نشط', default=True, render_kw={"class": "form-check-input"})
    
    submit = SubmitField('حفظ', render_kw={"class": "btn btn-primary"})

    def __init__(self, employee_id=None, *args, **kwargs):
        super(EmployeeForm, self).__init__(*args, **kwargs)
        self.employee_id = employee_id

    def validate_national_id(self, national_id):
        employee = Employee.query.filter_by(national_id=national_id.data).first()
        if employee and (not self.employee_id or employee.id != self.employee_id):
            raise ValidationError('الرقم الوطني مستخدم بالفعل.')

    def validate_employee_number(self, employee_number):
        employee = Employee.query.filter_by(employee_number=employee_number.data).first()
        if employee and (not self.employee_id or employee.id != self.employee_id):
            raise ValidationError('الرقم الوظيفي مستخدم بالفعل.')

    def validate_birth_date(self, birth_date):
        if birth_date.data and birth_date.data > date.today():
            raise ValidationError('تاريخ الميلاد لا يمكن أن يكون في المستقبل.')

    def validate_hire_date(self, hire_date):
        if hire_date.data and hire_date.data > date.today():
            raise ValidationError('تاريخ التعيين لا يمكن أن يكون في المستقبل.')

class EmployeeSearchForm(FlaskForm):
    search = StringField('البحث', render_kw={"class": "form-control", "placeholder": "ابحث بالاسم أو الرقم الوظيفي أو الرقم الوطني"})
    department = SelectField('القسم', choices=[('', 'جميع الأقسام')], render_kw={"class": "form-select"})
    contract_type = SelectField('نوع التعاقد', 
                               choices=[('', 'جميع الأنواع'), ('تعيين', 'تعيين'), ('عقد', 'عقد'), ('ندب', 'ندب'), ('انتقال', 'انتقال')], 
                               render_kw={"class": "form-select"})
    is_active = SelectField('الحالة', 
                           choices=[('', 'الكل'), ('1', 'نشط'), ('0', 'غير نشط')], 
                           render_kw={"class": "form-select"})
    submit = SubmitField('بحث', render_kw={"class": "btn btn-outline-primary"})
