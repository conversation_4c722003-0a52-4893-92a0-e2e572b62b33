from flask import render_template, request, make_response, jsonify
from flask_login import login_required
from app import db
from app.reports import bp
from app.models import Employee, Attendance, Leave, User
from app.auth.routes import hr_required
from datetime import datetime, date, timedelta
from sqlalchemy import and_, func, extract
import json

@bp.route('/')
@hr_required
def index():
    return render_template('reports/index.html', title='التقارير')

@bp.route('/attendance')
@hr_required
def attendance_report():
    # Get parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    employee_id = request.args.get('employee_id', type=int)
    
    # Default to current month if no dates provided
    if not start_date or not end_date:
        today = date.today()
        start_date = date(today.year, today.month, 1)
        end_date = today
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Build query
    query = db.session.query(
        Employee.id,
        Employee.first_name,
        Employee.last_name,
        Employee.employee_number,
        func.count(Attendance.id).label('total_days'),
        func.sum(func.case([(Attendance.status == 'present', 1)], else_=0)).label('present_days'),
        func.sum(func.case([(Attendance.status == 'absent', 1)], else_=0)).label('absent_days'),
        func.sum(func.case([(Attendance.status == 'late', 1)], else_=0)).label('late_days'),
        func.sum(Attendance.total_hours).label('total_hours'),
        func.sum(Attendance.overtime_hours).label('overtime_hours')
    ).outerjoin(Attendance, and_(
        Employee.id == Attendance.employee_id,
        Attendance.date >= start_date,
        Attendance.date <= end_date
    )).filter(Employee.is_active == True)
    
    if employee_id:
        query = query.filter(Employee.id == employee_id)
    
    query = query.group_by(Employee.id).order_by(Employee.first_name)
    
    report_data = query.all()
    
    # Get employee list for filter
    employees = Employee.query.filter_by(is_active=True).order_by(Employee.first_name).all()
    
    return render_template('reports/attendance.html', 
                         title='تقرير الحضور والانصراف',
                         report_data=report_data,
                         employees=employees,
                         start_date=start_date,
                         end_date=end_date,
                         selected_employee=employee_id)

@bp.route('/monthly_summary')
@hr_required
def monthly_summary():
    year = request.args.get('year', datetime.now().year, type=int)
    month = request.args.get('month', datetime.now().month, type=int)
    
    # Get monthly attendance summary
    monthly_data = db.session.query(
        func.extract('day', Attendance.date).label('day'),
        func.count(Attendance.id).label('total_records'),
        func.sum(func.case([(Attendance.status == 'present', 1)], else_=0)).label('present'),
        func.sum(func.case([(Attendance.status == 'absent', 1)], else_=0)).label('absent'),
        func.sum(func.case([(Attendance.status == 'late', 1)], else_=0)).label('late'),
        func.sum(Attendance.total_hours).label('total_hours')
    ).filter(
        and_(
            extract('year', Attendance.date) == year,
            extract('month', Attendance.date) == month
        )
    ).group_by(func.extract('day', Attendance.date)).order_by('day').all()
    
    # Get employee statistics for the month
    employee_stats = db.session.query(
        Employee.first_name,
        Employee.last_name,
        Employee.employee_number,
        func.count(Attendance.id).label('working_days'),
        func.sum(func.case([(Attendance.status == 'present', 1)], else_=0)).label('present_days'),
        func.sum(func.case([(Attendance.status == 'late', 1)], else_=0)).label('late_days'),
        func.sum(Attendance.total_hours).label('total_hours')
    ).join(Attendance).filter(
        and_(
            extract('year', Attendance.date) == year,
            extract('month', Attendance.date) == month,
            Employee.is_active == True
        )
    ).group_by(Employee.id).order_by(Employee.first_name).all()
    
    return render_template('reports/monthly_summary.html',
                         title=f'ملخص شهر {month}/{year}',
                         monthly_data=monthly_data,
                         employee_stats=employee_stats,
                         year=year,
                         month=month)

@bp.route('/employee_profile/<int:employee_id>')
@hr_required
def employee_profile(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    
    # Get attendance statistics
    total_attendance = Attendance.query.filter_by(employee_id=employee_id).count()
    present_days = Attendance.query.filter_by(employee_id=employee_id, status='present').count()
    late_days = Attendance.query.filter_by(employee_id=employee_id, status='late').count()
    absent_days = Attendance.query.filter_by(employee_id=employee_id, status='absent').count()
    
    # Get total working hours
    total_hours = db.session.query(func.sum(Attendance.total_hours)).filter_by(employee_id=employee_id).scalar() or 0
    overtime_hours = db.session.query(func.sum(Attendance.overtime_hours)).filter_by(employee_id=employee_id).scalar() or 0
    
    # Get recent attendance (last 30 days)
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_attendance = Attendance.query.filter(
        and_(
            Attendance.employee_id == employee_id,
            Attendance.date >= thirty_days_ago
        )
    ).order_by(Attendance.date.desc()).limit(30).all()
    
    # Get leave statistics
    total_leaves = Leave.query.filter_by(employee_id=employee_id).count()
    approved_leaves = Leave.query.filter_by(employee_id=employee_id, status='approved').count()
    pending_leaves = Leave.query.filter_by(employee_id=employee_id, status='pending').count()
    
    return render_template('reports/employee_profile.html',
                         title=f'ملف الموظف - {employee.full_name}',
                         employee=employee,
                         stats={
                             'total_attendance': total_attendance,
                             'present_days': present_days,
                             'late_days': late_days,
                             'absent_days': absent_days,
                             'total_hours': total_hours,
                             'overtime_hours': overtime_hours,
                             'total_leaves': total_leaves,
                             'approved_leaves': approved_leaves,
                             'pending_leaves': pending_leaves
                         },
                         recent_attendance=recent_attendance)

@bp.route('/leaves_summary')
@hr_required
def leaves_summary():
    year = request.args.get('year', datetime.now().year, type=int)
    
    # Get leave statistics by type
    leave_stats = db.session.query(
        Leave.leave_type,
        func.count(Leave.id).label('total_requests'),
        func.sum(func.case([(Leave.status == 'approved', 1)], else_=0)).label('approved'),
        func.sum(func.case([(Leave.status == 'pending', 1)], else_=0)).label('pending'),
        func.sum(func.case([(Leave.status == 'rejected', 1)], else_=0)).label('rejected'),
        func.sum(func.case([(Leave.status == 'approved', Leave.days_count)], else_=0)).label('approved_days')
    ).filter(
        extract('year', Leave.start_date) == year
    ).group_by(Leave.leave_type).all()
    
    # Get monthly leave distribution
    monthly_leaves = db.session.query(
        extract('month', Leave.start_date).label('month'),
        func.count(Leave.id).label('total_requests'),
        func.sum(Leave.days_count).label('total_days')
    ).filter(
        and_(
            extract('year', Leave.start_date) == year,
            Leave.status == 'approved'
        )
    ).group_by(extract('month', Leave.start_date)).order_by('month').all()
    
    return render_template('reports/leaves_summary.html',
                         title=f'ملخص الإجازات {year}',
                         leave_stats=leave_stats,
                         monthly_leaves=monthly_leaves,
                         year=year)
