import os
from flask_migrate import Migrate
from app import create_app, db
from app.models import User, Employee, Attendance, Document

app = create_app(os.getenv('FLASK_CONFIG') or 'default')
migrate = Migrate(app, db)

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'Employee': Employee, 
            'Attendance': Attendance, 'Document': Document}

@app.cli.command()
def init_db():
    """Initialize the database."""
    db.create_all()
    print('Database initialized.')

@app.cli.command()
def create_admin():
    """Create admin user."""
    from app.models import User
    import bcrypt
    
    admin = User.query.filter_by(username='admin').first()
    if admin:
        print('Admin user already exists.')
        return
    
    password = 'admin123'
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password_hash=hashed_password.decode('utf-8'),
        role='admin',
        is_active=True
    )
    
    db.session.add(admin)
    db.session.commit()
    print('Admin user created successfully.')
    print('Username: admin')
    print('Password: admin123')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
