@echo off
chcp 65001 >nul
title نظام شؤون الموظفين - HR System

echo ================================================================
echo                   نظام شؤون الموظفين المتقدم
echo                      HR Management System
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء البيئة الافتراضية
)

REM Activate virtual environment
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

REM Install requirements
echo 📥 تثبيت المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت جميع المتطلبات

REM Run the application
echo.
echo 🚀 بدء تشغيل نظام شؤون الموظفين...
echo.
echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo 🔐 بيانات تسجيل الدخول الافتراضية:
echo    المدير: admin / admin123
echo    الموارد البشرية: hr / hr123
echo.
echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo ================================================================
echo.

python run.py

echo.
echo 👋 تم إيقاف نظام شؤون الموظفين
pause
