import os
import uuid
from datetime import datetime
from PIL import Image
from werkzeug.utils import secure_filename
from flask import current_app, flash
import mimetypes

def allowed_file(filename, allowed_extensions=None):
    """Check if file extension is allowed"""
    if allowed_extensions is None:
        allowed_extensions = current_app.config['ALLOWED_EXTENSIONS']
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def get_file_type(filename):
    """Get file type based on extension"""
    if not filename:
        return 'unknown'
    
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'}
    document_extensions = {'pdf', 'doc', 'docx', 'txt', 'rtf'}
    
    if ext in image_extensions:
        return 'image'
    elif ext in document_extensions:
        return 'document'
    else:
        return 'other'

def generate_unique_filename(original_filename):
    """Generate unique filename while preserving extension"""
    if not original_filename:
        return str(uuid.uuid4())
    
    name, ext = os.path.splitext(secure_filename(original_filename))
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    
    return f"{name}_{timestamp}_{unique_id}{ext}"

def save_uploaded_file(file, subfolder='documents', max_size_mb=10, resize_images=True, max_dimensions=(1200, 1200)):
    """
    Save uploaded file to the uploads directory
    
    Args:
        file: FileStorage object from form
        subfolder: Subdirectory within uploads folder
        max_size_mb: Maximum file size in MB
        resize_images: Whether to resize images
        max_dimensions: Maximum image dimensions (width, height)
    
    Returns:
        dict: {'success': bool, 'filename': str, 'message': str, 'file_info': dict}
    """
    if not file or not file.filename:
        return {'success': False, 'message': 'لم يتم اختيار ملف'}
    
    # Check file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    
    if file_size > max_size_mb * 1024 * 1024:
        return {'success': False, 'message': f'حجم الملف كبير جداً. الحد الأقصى {max_size_mb}MB'}
    
    # Check file extension
    if not allowed_file(file.filename):
        return {'success': False, 'message': 'نوع الملف غير مسموح'}
    
    # Generate unique filename
    filename = generate_unique_filename(file.filename)
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], subfolder)
    os.makedirs(upload_dir, exist_ok=True)
    
    file_path = os.path.join(upload_dir, filename)
    relative_path = os.path.join(subfolder, filename).replace('\\', '/')
    
    try:
        file_type = get_file_type(file.filename)
        
        if file_type == 'image' and resize_images:
            # Process and resize image
            image = Image.open(file)
            
            # Convert RGBA to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Resize if larger than max dimensions
            if image.size[0] > max_dimensions[0] or image.size[1] > max_dimensions[1]:
                image.thumbnail(max_dimensions, Image.Resampling.LANCZOS)
            
            # Save with optimization
            image.save(file_path, optimize=True, quality=85)
        else:
            # Save file as-is
            file.save(file_path)
        
        # Get file info
        file_info = {
            'original_name': file.filename,
            'filename': filename,
            'size': os.path.getsize(file_path),
            'mime_type': mimetypes.guess_type(file_path)[0],
            'type': file_type,
            'path': relative_path
        }
        
        return {
            'success': True,
            'filename': relative_path,
            'message': 'تم رفع الملف بنجاح',
            'file_info': file_info
        }
        
    except Exception as e:
        # Clean up file if it was created
        if os.path.exists(file_path):
            os.remove(file_path)
        
        return {'success': False, 'message': f'خطأ في حفظ الملف: {str(e)}'}

def delete_file(file_path):
    """
    Delete file from uploads directory
    
    Args:
        file_path: Relative path to file within uploads directory
    
    Returns:
        dict: {'success': bool, 'message': str}
    """
    if not file_path:
        return {'success': False, 'message': 'مسار الملف غير محدد'}
    
    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
    
    try:
        if os.path.exists(full_path):
            os.remove(full_path)
            return {'success': True, 'message': 'تم حذف الملف بنجاح'}
        else:
            return {'success': False, 'message': 'الملف غير موجود'}
    except Exception as e:
        return {'success': False, 'message': f'خطأ في حذف الملف: {str(e)}'}

def get_file_info(file_path):
    """
    Get information about a file
    
    Args:
        file_path: Relative path to file within uploads directory
    
    Returns:
        dict: File information or None if file doesn't exist
    """
    if not file_path:
        return None
    
    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
    
    if not os.path.exists(full_path):
        return None
    
    try:
        stat = os.stat(full_path)
        filename = os.path.basename(file_path)
        
        return {
            'filename': filename,
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'mime_type': mimetypes.guess_type(full_path)[0],
            'type': get_file_type(filename),
            'path': file_path,
            'exists': True
        }
    except Exception:
        return None

def format_file_size(size_bytes):
    """
    Format file size in human readable format
    
    Args:
        size_bytes: Size in bytes
    
    Returns:
        str: Formatted size string
    """
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def create_thumbnail(image_path, thumbnail_path, size=(150, 150)):
    """
    Create thumbnail for image
    
    Args:
        image_path: Path to original image
        thumbnail_path: Path where thumbnail will be saved
        size: Thumbnail size (width, height)
    
    Returns:
        bool: Success status
    """
    try:
        with Image.open(image_path) as image:
            # Convert RGBA to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Create thumbnail directory if it doesn't exist
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            
            image.save(thumbnail_path, optimize=True, quality=85)
            return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

def cleanup_orphaned_files():
    """
    Clean up files that are not referenced in the database
    This should be run periodically as a maintenance task
    """
    from app.models import Employee, Document
    
    upload_dir = current_app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_dir):
        return
    
    # Get all files referenced in database
    referenced_files = set()
    
    # Employee photos and identity proofs
    employees = Employee.query.all()
    for emp in employees:
        if emp.photo:
            referenced_files.add(emp.photo)
        if emp.identity_proof:
            referenced_files.add(emp.identity_proof)
    
    # Document files
    documents = Document.query.filter_by(is_active=True).all()
    for doc in documents:
        if doc.file_path:
            referenced_files.add(doc.file_path)
    
    # Find orphaned files
    orphaned_files = []
    for root, dirs, files in os.walk(upload_dir):
        for file in files:
            file_path = os.path.relpath(os.path.join(root, file), upload_dir).replace('\\', '/')
            if file_path not in referenced_files:
                orphaned_files.append(file_path)
    
    return orphaned_files
