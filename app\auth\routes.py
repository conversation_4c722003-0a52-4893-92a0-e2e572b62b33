from datetime import datetime
from flask import render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from app import db
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm, ChangePasswordForm, UserManagementForm
from app.models import User, AuditLog
from functools import wraps

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.has_role('admin'):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def hr_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not (current_user.has_role('admin') or current_user.has_role('hr')):
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def log_action(action, table_name=None, record_id=None, old_values=None, new_values=None):
    """Log user actions for audit trail"""
    if current_user.is_authenticated:
        log = AuditLog(
            user_id=current_user.id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=str(old_values) if old_values else None,
            new_values=str(new_values) if new_values else None,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        db.session.add(log)
        db.session.commit()

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user is None or not user.check_password(form.password.data):
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            log_action('LOGIN_FAILED', old_values={'username': form.username.data})
            return redirect(url_for('auth.login'))
        
        if not user.is_active:
            flash('حسابك غير نشط. يرجى الاتصال بالمدير.', 'error')
            return redirect(url_for('auth.login'))
        
        login_user(user, remember=form.remember_me.data)
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        log_action('LOGIN_SUCCESS')
        flash(f'مرحباً {user.username}!', 'success')
        
        next_page = request.args.get('next')
        if not next_page or not next_page.startswith('/'):
            next_page = url_for('main.index')
        return redirect(next_page)
    
    return render_template('auth/login.html', title='تسجيل الدخول', form=form)

@bp.route('/logout')
@login_required
def logout():
    log_action('LOGOUT')
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register', methods=['GET', 'POST'])
@admin_required
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            role=form.role.data,
            is_active=True
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        
        log_action('USER_CREATED', 'user', user.id, 
                  new_values={'username': user.username, 'email': user.email, 'role': user.role})
        
        flash(f'تم إنشاء المستخدم {user.username} بنجاح!', 'success')
        return redirect(url_for('auth.users'))
    
    return render_template('auth/register.html', title='إنشاء مستخدم جديد', form=form)

@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return redirect(url_for('auth.change_password'))

        current_user.set_password(form.new_password.data)
        db.session.commit()

        log_action('PASSWORD_CHANGED')
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('main.profile'))

    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)

@bp.route('/users')
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=10, error_out=False)
    return render_template('auth/users.html', title='إدارة المستخدمين', users=users)

@bp.route('/user/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(id):
    user = User.query.get_or_404(id)
    form = UserManagementForm(obj=user)

    if form.validate_on_submit():
        old_values = {
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active
        }

        user.username = form.username.data
        user.email = form.email.data
        user.role = form.role.data
        user.is_active = form.is_active.data

        db.session.commit()

        new_values = {
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'is_active': user.is_active
        }

        log_action('USER_UPDATED', 'user', user.id, old_values, new_values)
        flash(f'تم تحديث بيانات المستخدم {user.username} بنجاح!', 'success')
        return redirect(url_for('auth.users'))

    return render_template('auth/edit_user.html', title='تعديل المستخدم', form=form, user=user)

@bp.route('/user/<int:id>/delete', methods=['POST'])
@admin_required
def delete_user(id):
    user = User.query.get_or_404(id)

    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('auth.users'))

    username = user.username
    log_action('USER_DELETED', 'user', user.id,
              old_values={'username': user.username, 'email': user.email, 'role': user.role})

    db.session.delete(user)
    db.session.commit()

    flash(f'تم حذف المستخدم {username} بنجاح!', 'success')
    return redirect(url_for('auth.users'))

@bp.route('/set_language/<language>')
def set_language(language):
    session['language'] = language
    return redirect(request.referrer or url_for('main.index'))
