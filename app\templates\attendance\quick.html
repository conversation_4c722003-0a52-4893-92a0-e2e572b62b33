{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-lg">
            <div class="card-header bg-success text-white text-center">
                <h3><i class="bi bi-clock"></i> تسجيل الحضور السريع</h3>
                <p class="mb-0">{{ moment().format('dddd، DD MMMM YYYY') }}</p>
            </div>
            <div class="card-body p-5">
                <form method="POST" id="quickAttendanceForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-floating">
                                {{ form.employee_number(class="form-control form-control-lg", autofocus=true) }}
                                {{ form.employee_number.label() }}
                            </div>
                            {% if form.employee_number.errors %}
                                <div class="text-danger mt-2">
                                    {% for error in form.employee_number.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            {{ form.action(class="form-select form-select-lg") }}
                            {% if form.action.errors %}
                                <div class="text-danger mt-2">
                                    {% for error in form.action.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12 text-center">
                            <div class="current-time">
                                <div class="time-display" id="currentTime"></div>
                                <div class="date-display text-muted" id="currentDate"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-success btn-lg") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Recent Attendance -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> آخر عمليات التسجيل اليوم</h5>
            </div>
            <div class="card-body">
                <div id="recentAttendance">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6><i class="bi bi-info-circle"></i> تعليمات الاستخدام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>تسجيل الدخول:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> أدخل الرقم الوظيفي</li>
                            <li><i class="bi bi-check-circle text-success"></i> اختر "تسجيل دخول"</li>
                            <li><i class="bi bi-check-circle text-success"></i> اضغط "تسجيل"</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>تسجيل الخروج:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> أدخل الرقم الوظيفي</li>
                            <li><i class="bi bi-check-circle text-success"></i> اختر "تسجيل خروج"</li>
                            <li><i class="bi bi-check-circle text-success"></i> اضغط "تسجيل"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.time-display {
    font-size: 3rem;
    font-weight: bold;
    color: #28a745;
    font-family: 'Courier New', monospace;
}

.date-display {
    font-size: 1.2rem;
    margin-top: 0.5rem;
}

.current-time {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    margin: 1rem 0;
}

.recent-attendance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.recent-attendance-item:last-child {
    border-bottom: none;
}

.employee-info {
    display: flex;
    align-items: center;
}

.employee-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
}

.action-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .time-display {
        font-size: 2rem;
    }
    
    .current-time {
        padding: 1rem;
    }
}
</style>

<script>
// Update current time
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    document.getElementById('currentTime').textContent = timeString;
    document.getElementById('currentDate').textContent = dateString;
}

// Load recent attendance
function loadRecentAttendance() {
    fetch('/attendance/recent_today')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentAttendance');
            if (data.length === 0) {
                container.innerHTML = '<div class="text-center text-muted">لا توجد عمليات تسجيل اليوم</div>';
                return;
            }
            
            container.innerHTML = data.map(item => `
                <div class="recent-attendance-item">
                    <div class="employee-info">
                        <div class="employee-avatar">
                            <i class="bi bi-person"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${item.employee_name}</div>
                            <div class="text-muted small">${item.employee_number}</div>
                        </div>
                    </div>
                    <div class="text-left">
                        <span class="badge ${item.action === 'check_in' ? 'bg-success' : 'bg-warning'} action-badge">
                            ${item.action === 'check_in' ? 'دخول' : 'خروج'}
                        </span>
                        <div class="text-muted small mt-1">${item.time}</div>
                    </div>
                </div>
            `).join('');
        })
        .catch(error => {
            console.error('Error loading recent attendance:', error);
            document.getElementById('recentAttendance').innerHTML = 
                '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
        });
}

// Auto-focus on employee number input after form submission
function resetForm() {
    document.getElementById('quickAttendanceForm').reset();
    document.querySelector('input[name="employee_number"]').focus();
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateTime();
    loadRecentAttendance();
    
    // Update time every second
    setInterval(updateTime, 1000);
    
    // Reload recent attendance every 30 seconds
    setInterval(loadRecentAttendance, 30000);
    
    // Handle form submission
    const form = document.getElementById('quickAttendanceForm');
    form.addEventListener('submit', function(e) {
        const employeeNumber = document.querySelector('input[name="employee_number"]').value.trim();
        if (!employeeNumber) {
            e.preventDefault();
            hrSystem.showNotification('يرجى إدخال الرقم الوظيفي', 'warning');
            return;
        }
        
        // Show loading
        hrSystem.showLoading();
        
        // Reset form after a short delay to allow for server response
        setTimeout(() => {
            hrSystem.hideLoading();
            loadRecentAttendance();
        }, 2000);
    });
    
    // Auto-focus on employee number input
    document.querySelector('input[name="employee_number"]').focus();
    
    // Clear input on double click
    document.querySelector('input[name="employee_number"]').addEventListener('dblclick', function() {
        this.value = '';
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // F1 for check in
    if (e.key === 'F1') {
        e.preventDefault();
        document.querySelector('select[name="action"]').value = 'check_in';
        document.querySelector('input[name="employee_number"]').focus();
    }
    
    // F2 for check out
    if (e.key === 'F2') {
        e.preventDefault();
        document.querySelector('select[name="action"]').value = 'check_out';
        document.querySelector('input[name="employee_number"]').focus();
    }
    
    // Escape to clear form
    if (e.key === 'Escape') {
        resetForm();
    }
});
</script>
{% endblock %}
