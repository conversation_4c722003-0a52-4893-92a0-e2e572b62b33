#!/bin/bash

# HR System Startup Script for Linux/Mac
# نظام شؤون الموظفين - سكريبت التشغيل

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}================================================================${NC}"
echo -e "${BLUE}                   نظام شؤون الموظفين المتقدم${NC}"
echo -e "${BLUE}                      HR Management System${NC}"
echo -e "${BLUE}================================================================${NC}"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 غير مثبت على النظام${NC}"
    echo -e "${YELLOW}يرجى تثبيت Python 3.8 أو أحدث${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم العثور على Python3${NC}"

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo -e "${RED}❌ Python 3.8 أو أحدث مطلوب. الإصدار الحالي: $PYTHON_VERSION${NC}"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}📦 إنشاء البيئة الافتراضية...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}✅ تم إنشاء البيئة الافتراضية${NC}"
fi

# Activate virtual environment
echo -e "${YELLOW}🔄 تفعيل البيئة الافتراضية...${NC}"
source venv/bin/activate

# Upgrade pip
echo -e "${YELLOW}📦 تحديث pip...${NC}"
pip install --upgrade pip --quiet

# Install requirements
echo -e "${YELLOW}📥 تثبيت المتطلبات...${NC}"
pip install -r requirements.txt --quiet

echo -e "${GREEN}✅ تم تثبيت جميع المتطلبات${NC}"

# Make the script executable
chmod +x run.py

echo
echo -e "${BLUE}🚀 بدء تشغيل نظام شؤون الموظفين...${NC}"
echo
echo -e "${GREEN}🌐 سيتم فتح النظام على: http://localhost:5000${NC}"
echo -e "${GREEN}🔐 بيانات تسجيل الدخول الافتراضية:${NC}"
echo -e "${GREEN}   المدير: admin / admin123${NC}"
echo -e "${GREEN}   الموارد البشرية: hr / hr123${NC}"
echo
echo -e "${YELLOW}💡 اضغط Ctrl+C لإيقاف الخادم${NC}"
echo -e "${BLUE}================================================================${NC}"
echo

# Run the application
python3 run.py

echo
echo -e "${BLUE}👋 تم إيقاف نظام شؤون الموظفين${NC}"
