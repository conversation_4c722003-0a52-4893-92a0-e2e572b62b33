# نظام شؤون الموظفين المتقدم

نظام شؤون موظفين احترافي مطور بلغة Python باستخدام Flask مع واجهة مستخدم متجاوبة باستخدام Bootstrap.

## المميزات الرئيسية

### 🔐 إدارة المستخدمين والأمان
- نظام تسجيل دخول آمن مع تشفير كلمات المرور باستخدام bcrypt
- أدوار وصلاحيات متعددة (مدير، موظف HR، مستخدم عادي)
- جلسات آمنة مع خروج تلقائي
- سجل مراجعة شامل لجميع العمليات

### 👥 إدارة الموظفين
- **البيانات الشخصية**: الاسم، اللقب، الرقم الوطني، تاريخ الميلاد، مكان الميلاد، العنوان، رقم الهاتف
- **البيانات الوظيفية**: الرقم الوظيفي، المسمى الوظيفي، المؤهل، تاريخ التعيين، تاريخ المباشرة، نوع التعاقد، الدرجة الوظيفية، العلاوة
- إضافة وتعديل وحذف الموظفين
- البحث والتصفية المتقدم
- عرض تفصيلي لبيانات كل موظف

### 📁 إدارة الملفات والمرفقات
- رفع الصور الشخصية للموظفين
- رفع المستندات (السيرة الذاتية، الشهادات، العقود، إثبات الهوية)
- دعم أنواع ملفات متعددة: PDF, DOC, DOCX, JPG, PNG
- ضغط وتحسين الصور تلقائياً
- إدارة شاملة للملفات (عرض، تحميل، حذف)

### ⏰ إدارة الحضور والانصراف
- تسجيل الحضور والانصراف اليومي
- تسجيل سريع باستخدام الرقم الوظيفي
- حساب ساعات العمل والإضافي تلقائياً
- تتبع فترات الاستراحة
- إدارة طلبات الإجازات (سنوية، مرضية، طارئة، أمومة)
- موافقة أو رفض طلبات الإجازات

### 📊 التقارير والإحصائيات
- تقارير الحضور والانصراف الشهرية
- تقارير الموظفين التفصيلية
- ملخص الإجازات السنوي
- إحصائيات شاملة للحضور والغياب
- إمكانية طباعة التقارير

### 🎨 واجهة المستخدم المتقدمة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم الوضع المظلم (Dark Mode)
- دعم اللغتين العربية والإنجليزية
- واجهة سهلة الاستخدام مع Bootstrap 5
- رسوم بيانية وإحصائيات تفاعلية

## متطلبات النظام

- Python 3.8 أو أحدث
- قاعدة بيانات SQLite (افتراضي) أو PostgreSQL/MySQL
- مساحة تخزين للملفات المرفوعة

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd NCSHR
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
قم بتعديل ملف `.env`:
```env
SECRET_KEY=your-very-secret-key-here
FLASK_APP=hr_system.py
FLASK_ENV=development
DATABASE_URL=sqlite:///hr_system.db
```

### 5. إنشاء قاعدة البيانات
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 6. إنشاء المستخدم الإداري
```bash
flask create-admin
```
سيتم إنشاء مستخدم إداري بالبيانات التالية:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 7. تشغيل التطبيق
```bash
python hr_system.py
```

سيعمل التطبيق على العنوان: `http://localhost:5000`

## الاستخدام

### تسجيل الدخول الأول
1. افتح المتصفح وانتقل إلى `http://localhost:5000`
2. استخدم بيانات المدير:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

### إضافة موظف جديد
1. من القائمة الرئيسية، اختر "الموظفين" > "إضافة موظف"
2. املأ البيانات الشخصية والوظيفية
3. ارفع الصورة الشخصية وإثبات الهوية
4. احفظ البيانات

### تسجيل الحضور
1. اختر "الحضور" > "تسجيل سريع"
2. أدخل الرقم الوظيفي
3. اختر نوع العملية (دخول/خروج)
4. اضغط "تسجيل"

### عرض التقارير
1. اختر "التقارير" من القائمة الرئيسية
2. حدد نوع التقرير المطلوب
3. اختر الفترة الزمنية والمرشحات
4. اعرض أو اطبع التقرير

## هيكل المشروع

```
NCSHR/
├── app/
│   ├── __init__.py
│   ├── models.py
│   ├── utils.py
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── forms.py
│   ├── main/
│   │   ├── __init__.py
│   │   └── routes.py
│   ├── employees/
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── forms.py
│   ├── attendance/
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── forms.py
│   ├── reports/
│   │   ├── __init__.py
│   │   └── routes.py
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── uploads/
│   └── templates/
│       ├── base.html
│       ├── auth/
│       ├── main/
│       ├── employees/
│       ├── attendance/
│       └── reports/
├── migrations/
├── config.py
├── hr_system.py
├── requirements.txt
├── .env
└── README.md
```

## الأدوار والصلاحيات

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين
- إدارة الموظفين
- عرض جميع التقارير
- إعدادات النظام

### موظف الموارد البشرية (HR)
- إدارة الموظفين
- إدارة الحضور والإجازات
- عرض التقارير
- موافقة طلبات الإجازات

### مستخدم عادي (User)
- عرض بياناته الشخصية فقط
- تقديم طلبات الإجازات
- عرض سجل حضوره

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- التحقق من صحة البيانات المدخلة
- سجل مراجعة شامل
- جلسات آمنة مع انتهاء صلاحية

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل تقديم pull request.

---

تم تطوير هذا النظام بواسطة فريق التطوير لخدمة إدارة الموارد البشرية بكفاءة وفعالية.
