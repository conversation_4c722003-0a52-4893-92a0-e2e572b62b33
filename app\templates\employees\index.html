{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-people"></i> إدارة الموظفين</h1>
            {% if current_user.role in ['admin', 'hr'] %}
            <a href="{{ url_for('employees.add') }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> إضافة موظف جديد
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        {{ form.search(placeholder="ابحث بالاسم أو الرقم الوظيفي...") }}
                    </div>
                    <div class="col-md-2">
                        {{ form.contract_type() }}
                    </div>
                    <div class="col-md-2">
                        {{ form.is_active() }}
                    </div>
                    <div class="col-md-2">
                        {{ form.submit() }}
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4">{{ employees.total }}</div>
                        <div>إجمالي الموظفين</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4">{{ employees.items | selectattr('is_active') | list | length }}</div>
                        <div>الموظفين النشطين</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4">{{ employees.items | rejectattr('is_active') | list | length }}</div>
                        <div>غير النشطين</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-x fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4">{{ employees.pages }}</div>
                        <div>عدد الصفحات</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees List -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة الموظفين</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" onclick="toggleView('table')">
                            <i class="bi bi-table"></i> جدول
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleView('cards')">
                            <i class="bi bi-grid"></i> بطاقات
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Table View -->
                <div id="tableView" class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>الرقم الوظيفي</th>
                                <th>المسمى الوظيفي</th>
                                <th>نوع التعاقد</th>
                                <th>تاريخ التعيين</th>
                                <th>الحالة</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td>
                                    {% if employee.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                             class="rounded-circle employee-photo" width="40" height="40" alt="صورة الموظف">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="bi bi-person text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="fw-bold">{{ employee.full_name }}</div>
                                    <div class="text-muted small">{{ employee.national_id }}</div>
                                </td>
                                <td>{{ employee.employee_number }}</td>
                                <td>{{ employee.job_title }}</td>
                                <td>
                                    <span class="badge bg-info">{{ employee.contract_type or 'غير محدد' }}</span>
                                </td>
                                <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد' }}</td>
                                <td>
                                    {% if employee.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td class="no-print">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('employees.view', id=employee.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        {% if current_user.role in ['admin', 'hr'] %}
                                        <a href="{{ url_for('employees.edit', id=employee.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% if employee.is_active %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="confirmDelete({{ employee.id }}, '{{ employee.full_name }}')" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                        {% else %}
                                        <form method="POST" action="{{ url_for('employees.activate', id=employee.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-outline-success" title="تفعيل">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="bi bi-info-circle"></i>
                                    لا توجد موظفين مطابقين للبحث
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Cards View (Hidden by default) -->
                <div id="cardsView" class="d-none p-3">
                    <div class="row">
                        {% for employee in employees.items %}
                        <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                            <div class="card employee-card h-100">
                                <div class="card-body text-center">
                                    {% if employee.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                             class="rounded-circle employee-photo mb-3" width="80" height="80" alt="صورة الموظف">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                             style="width: 80px; height: 80px;">
                                            <i class="bi bi-person text-white fa-2x"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <h6 class="card-title">{{ employee.full_name }}</h6>
                                    <p class="card-text text-muted">{{ employee.job_title }}</p>
                                    <p class="card-text">
                                        <small class="text-muted">{{ employee.employee_number }}</small>
                                    </p>
                                    
                                    {% if employee.is_active %}
                                        <span class="badge bg-success mb-2">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger mb-2">غير نشط</span>
                                    {% endif %}
                                    
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('employees.view', id=employee.id) }}" 
                                           class="btn btn-primary btn-sm">عرض التفاصيل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if employees.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if employees.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, **request.args) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in employees.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != employees.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if employees.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, **request.args) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموظف <strong id="employeeName"></strong>؟</p>
                <p class="text-muted">سيتم إلغاء تفعيل الموظف بدلاً من حذفه نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleView(viewType) {
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');
    const buttons = document.querySelectorAll('.btn-group button');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    
    if (viewType === 'table') {
        tableView.classList.remove('d-none');
        cardsView.classList.add('d-none');
        buttons[0].classList.add('active');
    } else {
        tableView.classList.add('d-none');
        cardsView.classList.remove('d-none');
        buttons[1].classList.add('active');
    }
}

function confirmDelete(employeeId, employeeName) {
    document.getElementById('employeeName').textContent = employeeName;
    document.getElementById('deleteForm').action = `/employees/${employeeId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
