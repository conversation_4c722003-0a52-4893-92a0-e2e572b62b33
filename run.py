#!/usr/bin/env python3
"""
HR System Application Runner
Simple script to run the HR system with proper configuration
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import flask_wtf
        import bcrypt
        import PIL
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing required package: {e}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False

def setup_environment():
    """Setup environment variables if not already set"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("Creating .env file with default settings...")
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("""SECRET_KEY=hr-system-secret-key-change-in-production
FLASK_APP=hr_system.py
FLASK_ENV=development
DATABASE_URL=sqlite:///hr_system.db
""")
        print("✓ .env file created")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

def check_database():
    """Check if database exists and is initialized"""
    db_path = Path('hr_system.db')
    
    if not db_path.exists():
        print("Database not found. Initializing...")
        try:
            from init_db import main as init_db
            init_db()
            print("✓ Database initialized successfully")
        except Exception as e:
            print(f"✗ Error initializing database: {e}")
            return False
    else:
        print("✓ Database found")
    
    return True

def create_upload_directories():
    """Create upload directories if they don't exist"""
    upload_dirs = [
        'app/static/uploads',
        'app/static/uploads/photos',
        'app/static/uploads/documents'
    ]
    
    for dir_path in upload_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✓ Upload directories created")

def main():
    """Main function to run the HR system"""
    print("=" * 60)
    print("🏢 HR System - نظام شؤون الموظفين")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Create upload directories
    create_upload_directories()
    
    # Check database
    if not check_database():
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🚀 Starting HR System...")
    print("=" * 60)
    
    # Import and run the application
    try:
        from hr_system import app
        
        print("\n📋 System Information:")
        print(f"   • Application: HR Management System")
        print(f"   • Version: 1.0.0")
        print(f"   • Environment: {os.getenv('FLASK_ENV', 'production')}")
        print(f"   • Database: {os.getenv('DATABASE_URL', 'sqlite:///hr_system.db')}")
        
        print("\n🔐 Default Login Credentials:")
        print("   • Admin: username=admin, password=admin123")
        print("   • HR: username=hr, password=hr123")
        
        print("\n🌐 Server Information:")
        print("   • URL: http://localhost:5000")
        print("   • Press Ctrl+C to stop the server")
        
        print("\n" + "=" * 60)
        
        # Run the application
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=os.getenv('FLASK_ENV') == 'development',
            use_reloader=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 HR System stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting HR System: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
