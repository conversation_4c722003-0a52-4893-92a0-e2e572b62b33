/* Custom CSS for HR System */

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Dark Mode */
.dark-mode {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

.dark-mode .navbar {
    background-color: #2d3748 !important;
}

.dark-mode .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #ffffff !important;
}

.dark-mode .card-header {
    background-color: #4a5568 !important;
    border-color: #4a5568 !important;
}

.dark-mode .table {
    color: #ffffff !important;
}

.dark-mode .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: #4a5568 !important;
}

.dark-mode .form-control,
.dark-mode .form-select {
    background-color: #4a5568 !important;
    border-color: #718096 !important;
    color: #ffffff !important;
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: #4a5568 !important;
    border-color: #63b3ed !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
}

.dark-mode .dropdown-menu {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
}

.dark-mode .dropdown-item {
    color: #ffffff !important;
}

.dark-mode .dropdown-item:hover {
    background-color: #4a5568 !important;
}

.dark-mode .alert {
    border-color: #4a5568 !important;
}

.dark-mode .modal-content {
    background-color: #2d3748 !important;
    color: #ffffff !important;
}

.dark-mode .modal-header {
    border-color: #4a5568 !important;
}

.dark-mode .modal-footer {
    border-color: #4a5568 !important;
}

/* Custom Components */
.employee-card {
    transition: transform 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.employee-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.employee-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.attendance-status {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 5px;
}

.status-present {
    background-color: #28a745;
}

.status-absent {
    background-color: #dc3545;
}

.status-late {
    background-color: #ffc107;
}

.status-half-day {
    background-color: #17a2b8;
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Quick Actions */
.quick-action-btn {
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.quick-action-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Tables */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Forms */
.form-floating > label {
    right: 0.75rem;
    left: auto;
}

.form-floating > .form-control,
.form-floating > .form-select {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .employee-card {
        margin-bottom: 1rem;
    }
    
    .stat-card .stat-number {
        font-size: 1.5rem;
    }
    
    .quick-action-btn {
        height: 80px;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border-custom {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}
