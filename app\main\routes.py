from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from app.main import bp
from app.models import Employee, User, Attendance, Leave, Document
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    # Dashboard statistics
    stats = {}
    
    # Total employees
    stats['total_employees'] = Employee.query.filter_by(is_active=True).count()
    
    # Today's attendance
    today = date.today()
    stats['present_today'] = Attendance.query.filter(
        and_(Attendance.date == today, Attendance.status == 'present')
    ).count()
    
    # Pending leaves
    stats['pending_leaves'] = Leave.query.filter_by(status='pending').count()
    
    # Active users
    stats['active_users'] = User.query.filter_by(is_active=True).count()
    
    # Recent activities (last 10)
    recent_employees = Employee.query.filter_by(is_active=True).order_by(
        Employee.created_at.desc()
    ).limit(5).all()
    
    # Monthly attendance summary
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    monthly_attendance = db.session.query(
        func.count(Attendance.id).label('total_records'),
        func.sum(func.case([(Attendance.status == 'present', 1)], else_=0)).label('present'),
        func.sum(func.case([(Attendance.status == 'absent', 1)], else_=0)).label('absent'),
        func.sum(func.case([(Attendance.status == 'late', 1)], else_=0)).label('late')
    ).filter(
        and_(
            func.extract('month', Attendance.date) == current_month,
            func.extract('year', Attendance.date) == current_year
        )
    ).first()
    
    return render_template('main/index.html', 
                         title='لوحة التحكم',
                         stats=stats,
                         recent_employees=recent_employees,
                         monthly_attendance=monthly_attendance)

@bp.route('/profile')
@login_required
def profile():
    return render_template('main/profile.html', title='الملف الشخصي')

@bp.route('/search')
@login_required
def search():
    query = request.args.get('q', '')
    if not query:
        return jsonify({'results': []})
    
    # Search employees
    employees = Employee.query.filter(
        db.or_(
            Employee.first_name.contains(query),
            Employee.last_name.contains(query),
            Employee.employee_number.contains(query),
            Employee.national_id.contains(query)
        )
    ).filter_by(is_active=True).limit(10).all()
    
    results = []
    for emp in employees:
        results.append({
            'type': 'employee',
            'id': emp.id,
            'title': emp.full_name,
            'subtitle': f'رقم الموظف: {emp.employee_number}',
            'url': f'/employees/{emp.id}'
        })
    
    return jsonify({'results': results})

@bp.route('/notifications')
@login_required
def notifications():
    notifications = []
    
    # Pending leaves for HR/Admin
    if current_user.role in ['admin', 'hr']:
        pending_leaves = Leave.query.filter_by(status='pending').limit(5).all()
        for leave in pending_leaves:
            notifications.append({
                'type': 'leave_request',
                'title': f'طلب إجازة من {leave.employee.full_name}',
                'message': f'من {leave.start_date} إلى {leave.end_date}',
                'url': f'/attendance/leaves/{leave.id}',
                'time': leave.applied_date
            })
    
    # Today's birthdays
    today = date.today()
    birthday_employees = Employee.query.filter(
        and_(
            func.extract('month', Employee.birth_date) == today.month,
            func.extract('day', Employee.birth_date) == today.day,
            Employee.is_active == True
        )
    ).all()
    
    for emp in birthday_employees:
        notifications.append({
            'type': 'birthday',
            'title': f'عيد ميلاد {emp.full_name}',
            'message': 'عيد ميلاد سعيد!',
            'url': f'/employees/{emp.id}',
            'time': datetime.now()
        })
    
    return jsonify({'notifications': notifications})

@bp.route('/quick_stats')
@login_required
def quick_stats():
    """API endpoint for dashboard quick stats"""
    today = date.today()
    
    # Today's attendance summary
    today_attendance = db.session.query(
        func.count(Attendance.id).label('total'),
        func.sum(func.case([(Attendance.status == 'present', 1)], else_=0)).label('present'),
        func.sum(func.case([(Attendance.status == 'absent', 1)], else_=0)).label('absent'),
        func.sum(func.case([(Attendance.status == 'late', 1)], else_=0)).label('late')
    ).filter(Attendance.date == today).first()
    
    # This week's new employees
    week_ago = today - timedelta(days=7)
    new_employees_week = Employee.query.filter(
        and_(
            Employee.created_at >= week_ago,
            Employee.is_active == True
        )
    ).count()
    
    return jsonify({
        'today_attendance': {
            'total': today_attendance.total or 0,
            'present': today_attendance.present or 0,
            'absent': today_attendance.absent or 0,
            'late': today_attendance.late or 0
        },
        'new_employees_week': new_employees_week
    })
