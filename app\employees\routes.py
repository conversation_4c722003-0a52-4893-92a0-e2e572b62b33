import os
from flask import render_template, redirect, url_for, flash, request, current_app, send_file, abort
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from PIL import Image
from app import db
from app.employees import bp
from app.employees.forms import <PERSON>p<PERSON>eeForm, EmployeeSearchForm
from app.models import Employee, Document, AuditLog
from app.auth.routes import hr_required, log_action
from datetime import datetime
import uuid

def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file, folder, max_size=(800, 600)):
    """Save uploaded file and return the filename"""
    if file and file.filename:
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        name, ext = os.path.splitext(filename)
        filename = f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{ext}"
        
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], folder, filename)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Save and resize image if it's an image
        if folder == 'photos' and ext.lower() in ['.jpg', '.jpeg', '.png']:
            image = Image.open(file)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            image.save(filepath, optimize=True, quality=85)
        else:
            file.save(filepath)
        
        return os.path.join(folder, filename)
    return None

@bp.route('/')
@login_required
def index():
    form = EmployeeSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Employee.query
    
    # Apply filters
    search = request.args.get('search', '')
    if search:
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.employee_number.contains(search),
                Employee.national_id.contains(search)
            )
        )
    
    contract_type = request.args.get('contract_type', '')
    if contract_type:
        query = query.filter(Employee.contract_type == contract_type)
    
    is_active = request.args.get('is_active', '')
    if is_active:
        query = query.filter(Employee.is_active == (is_active == '1'))
    
    # Order by creation date (newest first)
    query = query.order_by(Employee.created_at.desc())
    
    employees = query.paginate(
        page=page, per_page=current_app.config['EMPLOYEES_PER_PAGE'], error_out=False)
    
    return render_template('employees/index.html', 
                         title='إدارة الموظفين', 
                         employees=employees, 
                         form=form)

@bp.route('/add', methods=['GET', 'POST'])
@hr_required
def add():
    form = EmployeeForm()
    if form.validate_on_submit():
        employee = Employee(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            national_id=form.national_id.data,
            birth_date=form.birth_date.data,
            birth_place=form.birth_place.data,
            address=form.address.data,
            phone=form.phone.data,
            employee_number=form.employee_number.data,
            job_title=form.job_title.data,
            qualification=form.qualification.data,
            hire_date=form.hire_date.data,
            start_date=form.start_date.data,
            contract_type=form.contract_type.data,
            job_grade=form.job_grade.data,
            allowance=form.allowance.data or 0.0,
            is_active=form.is_active.data,
            created_by=current_user.id
        )
        
        # Handle file uploads
        if form.photo.data:
            photo_path = save_uploaded_file(form.photo.data, 'photos')
            if photo_path:
                employee.photo = photo_path
        
        if form.identity_proof.data:
            identity_path = save_uploaded_file(form.identity_proof.data, 'documents')
            if identity_path:
                employee.identity_proof = identity_path
        
        db.session.add(employee)
        db.session.commit()
        
        log_action('EMPLOYEE_CREATED', 'employee', employee.id, 
                  new_values={'name': employee.full_name, 'employee_number': employee.employee_number})
        
        flash(f'تم إضافة الموظف {employee.full_name} بنجاح!', 'success')
        return redirect(url_for('employees.view', id=employee.id))
    
    return render_template('employees/add.html', title='إضافة موظف جديد', form=form)

@bp.route('/<int:id>')
@login_required
def view(id):
    employee = Employee.query.get_or_404(id)
    documents = Document.query.filter_by(employee_id=id, is_active=True).all()
    return render_template('employees/view.html',
                         title=f'بيانات الموظف - {employee.full_name}',
                         employee=employee,
                         documents=documents)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@hr_required
def edit(id):
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(employee_id=id, obj=employee)

    if form.validate_on_submit():
        # Store old values for audit
        old_values = {
            'name': employee.full_name,
            'employee_number': employee.employee_number,
            'job_title': employee.job_title,
            'is_active': employee.is_active
        }

        # Update employee data
        form.populate_obj(employee)
        employee.updated_at = datetime.utcnow()

        # Handle file uploads
        if form.photo.data:
            photo_path = save_uploaded_file(form.photo.data, 'photos')
            if photo_path:
                employee.photo = photo_path

        if form.identity_proof.data:
            identity_path = save_uploaded_file(form.identity_proof.data, 'documents')
            if identity_path:
                employee.identity_proof = identity_path

        db.session.commit()

        new_values = {
            'name': employee.full_name,
            'employee_number': employee.employee_number,
            'job_title': employee.job_title,
            'is_active': employee.is_active
        }

        log_action('EMPLOYEE_UPDATED', 'employee', employee.id, old_values, new_values)

        flash(f'تم تحديث بيانات الموظف {employee.full_name} بنجاح!', 'success')
        return redirect(url_for('employees.view', id=employee.id))

    return render_template('employees/edit.html',
                         title=f'تعديل بيانات الموظف - {employee.full_name}',
                         form=form, employee=employee)

@bp.route('/<int:id>/delete', methods=['POST'])
@hr_required
def delete(id):
    employee = Employee.query.get_or_404(id)

    # Soft delete - just mark as inactive
    employee.is_active = False
    employee.updated_at = datetime.utcnow()
    db.session.commit()

    log_action('EMPLOYEE_DELETED', 'employee', employee.id,
              old_values={'name': employee.full_name, 'employee_number': employee.employee_number})

    flash(f'تم حذف الموظف {employee.full_name} بنجاح!', 'success')
    return redirect(url_for('employees.index'))

@bp.route('/<int:id>/activate', methods=['POST'])
@hr_required
def activate(id):
    employee = Employee.query.get_or_404(id)
    employee.is_active = True
    employee.updated_at = datetime.utcnow()
    db.session.commit()

    log_action('EMPLOYEE_ACTIVATED', 'employee', employee.id,
              new_values={'name': employee.full_name, 'employee_number': employee.employee_number})

    flash(f'تم تفعيل الموظف {employee.full_name} بنجاح!', 'success')
    return redirect(url_for('employees.view', id=id))
