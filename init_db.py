#!/usr/bin/env python3
"""
Database initialization script for HR System
This script creates the database tables and adds sample data
"""

import os
import sys
from datetime import datetime, date
import bcrypt

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Employee, Attendance, Leave, Document, SystemSettings

def create_admin_user():
    """Create default admin user"""
    admin = User.query.filter_by(username='admin').first()
    if admin:
        print('Admin user already exists.')
        return admin
    
    password = 'admin123'
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password_hash=hashed_password.decode('utf-8'),
        role='admin',
        is_active=True
    )
    
    db.session.add(admin)
    db.session.commit()
    
    print('Admin user created successfully.')
    print('Username: admin')
    print('Password: admin123')
    return admin

def create_hr_user():
    """Create default HR user"""
    hr_user = User.query.filter_by(username='hr').first()
    if hr_user:
        print('HR user already exists.')
        return hr_user
    
    password = 'hr123'
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
    
    hr_user = User(
        username='hr',
        email='<EMAIL>',
        password_hash=hashed_password.decode('utf-8'),
        role='hr',
        is_active=True
    )
    
    db.session.add(hr_user)
    db.session.commit()
    
    print('HR user created successfully.')
    print('Username: hr')
    print('Password: hr123')
    return hr_user

def create_sample_employees():
    """Create sample employees for testing"""
    if Employee.query.count() > 0:
        print('Sample employees already exist.')
        return
    
    admin = User.query.filter_by(username='admin').first()
    
    sample_employees = [
        {
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'national_id': '1234567890',
            'birth_date': date(1985, 5, 15),
            'birth_place': 'الرياض',
            'address': 'الرياض، حي النخيل',
            'phone': '0501234567',
            'employee_number': 'EMP001',
            'job_title': 'مطور برمجيات',
            'qualification': 'بكالوريوس علوم حاسوب',
            'hire_date': date(2020, 1, 15),
            'start_date': date(2020, 2, 1),
            'contract_type': 'تعيين',
            'job_grade': 'الدرجة السابعة',
            'allowance': 2000.0,
            'is_active': True,
            'created_by': admin.id
        },
        {
            'first_name': 'فاطمة',
            'last_name': 'علي',
            'national_id': '2345678901',
            'birth_date': date(1990, 8, 22),
            'birth_place': 'جدة',
            'address': 'جدة، حي الصفا',
            'phone': '0502345678',
            'employee_number': 'EMP002',
            'job_title': 'محاسبة',
            'qualification': 'بكالوريوس محاسبة',
            'hire_date': date(2021, 3, 10),
            'start_date': date(2021, 3, 15),
            'contract_type': 'عقد',
            'job_grade': 'الدرجة السادسة',
            'allowance': 1500.0,
            'is_active': True,
            'created_by': admin.id
        },
        {
            'first_name': 'خالد',
            'last_name': 'السعد',
            'national_id': '3456789012',
            'birth_date': date(1988, 12, 5),
            'birth_place': 'الدمام',
            'address': 'الدمام، حي الشاطئ',
            'phone': '0503456789',
            'employee_number': 'EMP003',
            'job_title': 'مدير مشروع',
            'qualification': 'ماجستير إدارة أعمال',
            'hire_date': date(2019, 6, 1),
            'start_date': date(2019, 6, 15),
            'contract_type': 'تعيين',
            'job_grade': 'الدرجة الثامنة',
            'allowance': 3000.0,
            'is_active': True,
            'created_by': admin.id
        },
        {
            'first_name': 'نورا',
            'last_name': 'الأحمد',
            'national_id': '4567890123',
            'birth_date': date(1992, 3, 18),
            'birth_place': 'مكة',
            'address': 'مكة، حي العزيزية',
            'phone': '0504567890',
            'employee_number': 'EMP004',
            'job_title': 'مصممة جرافيك',
            'qualification': 'بكالوريوس فنون جميلة',
            'hire_date': date(2022, 1, 20),
            'start_date': date(2022, 2, 1),
            'contract_type': 'عقد',
            'job_grade': 'الدرجة الخامسة',
            'allowance': 1200.0,
            'is_active': True,
            'created_by': admin.id
        },
        {
            'first_name': 'عبدالله',
            'last_name': 'الغامدي',
            'national_id': '5678901234',
            'birth_date': date(1987, 7, 30),
            'birth_place': 'أبها',
            'address': 'أبها، حي المنهل',
            'phone': '0505678901',
            'employee_number': 'EMP005',
            'job_title': 'مهندس شبكات',
            'qualification': 'بكالوريوس هندسة حاسوب',
            'hire_date': date(2020, 9, 1),
            'start_date': date(2020, 9, 15),
            'contract_type': 'تعيين',
            'job_grade': 'الدرجة السابعة',
            'allowance': 2200.0,
            'is_active': False,  # Inactive employee for testing
            'created_by': admin.id
        }
    ]
    
    for emp_data in sample_employees:
        employee = Employee(**emp_data)
        db.session.add(employee)
    
    db.session.commit()
    print(f'Created {len(sample_employees)} sample employees.')

def create_sample_attendance():
    """Create sample attendance records"""
    if Attendance.query.count() > 0:
        print('Sample attendance records already exist.')
        return
    
    employees = Employee.query.filter_by(is_active=True).all()
    if not employees:
        print('No active employees found. Create employees first.')
        return
    
    # Create attendance for the last 7 days
    from datetime import timedelta
    
    sample_records = []
    for i in range(7):
        attendance_date = date.today() - timedelta(days=i)
        
        for employee in employees[:3]:  # Only for first 3 employees
            attendance = Attendance(
                employee_id=employee.id,
                date=attendance_date,
                check_in=datetime.strptime('08:00', '%H:%M').time(),
                check_out=datetime.strptime('17:00', '%H:%M').time(),
                break_start=datetime.strptime('12:00', '%H:%M').time(),
                break_end=datetime.strptime('13:00', '%H:%M').time(),
                status='present',
                notes=f'حضور عادي - {attendance_date}'
            )
            attendance.calculate_hours()
            sample_records.append(attendance)
    
    for record in sample_records:
        db.session.add(record)
    
    db.session.commit()
    print(f'Created {len(sample_records)} sample attendance records.')

def create_system_settings():
    """Create default system settings"""
    settings = [
        {
            'setting_key': 'company_name',
            'setting_value': 'شركة النظم المتقدمة',
            'description': 'اسم الشركة'
        },
        {
            'setting_key': 'work_start_time',
            'setting_value': '08:00',
            'description': 'وقت بداية العمل'
        },
        {
            'setting_key': 'work_end_time',
            'setting_value': '17:00',
            'description': 'وقت نهاية العمل'
        },
        {
            'setting_key': 'break_duration',
            'setting_value': '60',
            'description': 'مدة الاستراحة بالدقائق'
        },
        {
            'setting_key': 'late_threshold',
            'setting_value': '15',
            'description': 'حد التأخير بالدقائق'
        }
    ]
    
    for setting_data in settings:
        existing = SystemSettings.query.filter_by(setting_key=setting_data['setting_key']).first()
        if not existing:
            setting = SystemSettings(**setting_data)
            db.session.add(setting)
    
    db.session.commit()
    print('System settings created successfully.')

def main():
    """Main initialization function"""
    print('Initializing HR System Database...')
    
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        # Create all tables
        print('Creating database tables...')
        db.create_all()
        print('Database tables created successfully.')
        
        # Create default users
        print('\nCreating default users...')
        create_admin_user()
        create_hr_user()
        
        # Create sample data
        print('\nCreating sample data...')
        create_sample_employees()
        create_sample_attendance()
        create_system_settings()
        
        print('\n' + '='*50)
        print('Database initialization completed successfully!')
        print('='*50)
        print('\nDefault login credentials:')
        print('Admin - Username: admin, Password: admin123')
        print('HR    - Username: hr, Password: hr123')
        print('\nYou can now start the application with:')
        print('python hr_system.py')

if __name__ == '__main__':
    main()
