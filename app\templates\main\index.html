{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i>
            لوحة التحكم
            <small class="text-muted">مرحباً {{ current_user.username }}</small>
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الموظفين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_employees }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الحضور اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.present_today }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            طلبات الإجازات المعلقة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.pending_leaves }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            المستخدمين النشطين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Monthly Attendance Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">ملخص الحضور الشهري</h6>
            </div>
            <div class="card-body">
                {% if monthly_attendance %}
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">{{ monthly_attendance.present or 0 }}</div>
                            <div class="text-muted">حاضر</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-danger">{{ monthly_attendance.absent or 0 }}</div>
                            <div class="text-muted">غائب</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">{{ monthly_attendance.late or 0 }}</div>
                            <div class="text-muted">متأخر</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">{{ monthly_attendance.total_records or 0 }}</div>
                            <div class="text-muted">إجمالي</div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    لا توجد بيانات حضور لهذا الشهر
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Employees -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الموظفين الجدد</h6>
            </div>
            <div class="card-body">
                {% if recent_employees %}
                    {% for employee in recent_employees %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            {% if employee.photo %}
                                <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                     class="rounded-circle" width="40" height="40" alt="صورة الموظف">
                            {% else %}
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ employee.full_name }}</div>
                            <div class="text-muted small">{{ employee.job_title }}</div>
                        </div>
                        <div class="text-muted small">
                            {{ employee.created_at.strftime('%Y-%m-%d') }}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    لا توجد موظفين جدد
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
{% if current_user.role in ['admin', 'hr'] %}
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('employees.add') }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-person-plus"></i><br>
                            إضافة موظف
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('attendance.quick_attendance') }}" class="btn btn-success btn-lg w-100">
                            <i class="bi bi-clock"></i><br>
                            تسجيل حضور
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('attendance.add_leave') }}" class="btn btn-warning btn-lg w-100">
                            <i class="bi bi-calendar-x"></i><br>
                            طلب إجازة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.attendance_report') }}" class="btn btn-info btn-lg w-100">
                            <i class="bi bi-graph-up"></i><br>
                            التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
{% endblock %}
